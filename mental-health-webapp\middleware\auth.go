// middleware/auth.go
package middleware

import (
	"strconv"

	"github.com/gofiber/fiber/v2"
	jwtware "github.com/gofiber/jwt/v3"
	"github.com/golang-jwt/jwt/v4"
)

// JWTProtected creates a JWT middleware using the provided secret
func JWTProtected(secret string) fiber.Handler {
	return jwtware.New(jwtware.Config{
		SigningKey: []byte(secret),
		SuccessHandler: func(c *fiber.Ctx) error {
			// Extract user ID from JWT token and add to context
			user := c.Locals("user").(*jwt.Token)
			claims := user.Claims.(jwt.MapClaims)

			// Get user ID from claims
			userIDFloat, ok := claims["sub"].(float64)
			if !ok {
				return c.Status(401).JSON(fiber.Map{
					"error": "Invalid token: missing user ID",
				})
			}

			userID := int(userIDFloat)

			// Store user ID in context for use in handlers
			c.Locals("userID", userID)
			c.Locals("userIDStr", strconv.Itoa(userID))

			return c.Next()
		},
	})
}
